import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useFetchSupervisorDashboardStats = () => {
    const query = useQuery({
        queryKey: ["supervisor-dashboard-stats"],
        queryFn: async () => {
            try {
                const res = await Supervisor.getDashboardStats();
                if (res.success && res.data) {
                    return res.data;
                }
                return {
                    total_students: 0,
                    students_with_logbooks_this_week: 0,
                    students_logged_this_week: 0,
                    students_with_submitted_reports: 0,
                    students_with_pending_reports: 0,
                    students_with_final_reports: 0,
                    students_with_supervisor_feedback: 0
                };
            } catch (error: any) {
                console.error('Failed to fetch supervisor dashboard stats:', error);
                return {
                    total_students: 0,
                    students_with_logbooks_this_week: 0,
                    students_logged_this_week: 0,
                    students_with_submitted_reports: 0,
                    students_with_pending_reports: 0,
                    students_with_final_reports: 0,
                    students_with_supervisor_feedback: 0
                };
            }
        },
        refetchInterval: 60000, // Refetch every minute
        staleTime: 30000, // Consider data stale after 30 seconds
    });
    
    return query;
};
