-- Update existing final_reports table to match simplified structure
-- This script will modify the existing table to work with the new simplified approach

USE iptms_v2;

-- First, let's check if the table exists and what columns it has
-- DESCRIBE final_reports;

-- If the table exists with the old structure, we need to update it
-- If it doesn't exist, we'll create it with the new structure

-- Option 1: If you want to keep existing data and migrate it
-- Backup existing data first
CREATE TABLE IF NOT EXISTS final_reports_backup AS SELECT * FROM final_reports;

-- Option 2: Drop and recreate (WARNING: This will lose all data!)
-- DROP TABLE IF EXISTS final_reports;

-- Create the new simplified table structure
CREATE TABLE IF NOT EXISTS final_reports_new (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL,
  `title` varchar(255) NOT NULL DEFAULT 'Final Internship Report',
  `submission_type` enum('write','upload') NOT NULL DEFAULT 'write',
  
  -- Single content field for rich text editor
  `content_html` longtext COMMENT 'Rich text HTML content from editor',
  `content_plain` longtext COMMENT 'Plain text version for search/analysis',
  
  -- File upload fields
  `file_url` varchar(500) DEFAULT NULL COMMENT 'URL/path to uploaded file',
  `file_name` varchar(255) DEFAULT NULL COMMENT 'Original filename',
  `file_size` bigint DEFAULT NULL COMMENT 'File size in bytes',
  `file_type` varchar(100) DEFAULT NULL COMMENT 'MIME type of uploaded file',
  
  -- Metadata
  `character_count` int DEFAULT NULL COMMENT 'Character count of content',
  `word_count` int DEFAULT NULL COMMENT 'Word count of content',
  
  -- Status and workflow
  `status` enum('draft','submitted','reviewed','approved','needs_revision') NOT NULL DEFAULT 'draft',
  `is_submitted` tinyint(1) DEFAULT '0',
  `submitted_at` timestamp NULL DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  
  -- Supervisor feedback
  `supervisor_id` int DEFAULT NULL,
  `supervisor_comments` text COMMENT 'Supervisor feedback on the report',
  `supervisor_rating` int DEFAULT NULL COMMENT 'Rating from 1-10',
  `grade` varchar(10) DEFAULT NULL COMMENT 'Final grade (A, B, C, etc.)',
  
  -- Timestamps
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_final_report` (`student_id`),
  KEY `idx_final_reports_student_id` (`student_id`),
  KEY `idx_final_reports_status` (`status`),
  KEY `idx_final_reports_submitted` (`is_submitted`),
  KEY `idx_final_reports_supervisor_id` (`supervisor_id`),
  
  -- Foreign key constraints (uncomment if users table exists)
  -- CONSTRAINT `fk_final_reports_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  -- CONSTRAINT `fk_final_reports_supervisor` FOREIGN KEY (`supervisor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  
  -- Check constraints
  CONSTRAINT `chk_final_reports_rating` CHECK (`supervisor_rating` >= 1 AND `supervisor_rating` <= 10),
  CONSTRAINT `chk_final_reports_content` CHECK (
    (`submission_type` = 'write' AND `content_html` IS NOT NULL) OR 
    (`submission_type` = 'upload' AND `file_url` IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Migrate existing data if the old table exists
-- This combines all the separate content fields into the single content_html field
INSERT IGNORE INTO final_reports_new (
  student_id, title, submission_type, content_html, content_plain,
  file_url, file_name, file_size, file_type, character_count, word_count,
  status, is_submitted, submitted_at, reviewed_at, supervisor_id,
  supervisor_comments, supervisor_rating, grade, created_at, updated_at
)
SELECT 
  student_id, 
  title, 
  submission_type,
  -- Combine all separate content fields into single HTML content
  CASE 
    WHEN submission_type = 'write' THEN
      CONCAT_WS('<br><br>',
        CASE WHEN executive_summary IS NOT NULL AND executive_summary != '' THEN CONCAT('<h3>Executive Summary</h3>', COALESCE(executive_summary_html, executive_summary)) END,
        CASE WHEN objectives_achieved IS NOT NULL AND objectives_achieved != '' THEN CONCAT('<h3>Objectives Achieved</h3>', COALESCE(objectives_achieved_html, objectives_achieved)) END,
        CASE WHEN challenges_faced IS NOT NULL AND challenges_faced != '' THEN CONCAT('<h3>Challenges Faced</h3>', COALESCE(challenges_faced_html, challenges_faced)) END,
        CASE WHEN skills_acquired IS NOT NULL AND skills_acquired != '' THEN CONCAT('<h3>Skills Acquired</h3>', COALESCE(skills_acquired_html, skills_acquired)) END,
        CASE WHEN recommendations IS NOT NULL AND recommendations != '' THEN CONCAT('<h3>Recommendations</h3>', COALESCE(recommendations_html, recommendations)) END,
        CASE WHEN conclusion IS NOT NULL AND conclusion != '' THEN CONCAT('<h3>Conclusion</h3>', COALESCE(conclusion_html, conclusion)) END,
        CASE WHEN content IS NOT NULL AND content != '' THEN CONCAT('<h3>Additional Content</h3>', COALESCE(content_html, content)) END
      )
    ELSE content_html
  END as content_html,
  COALESCE(content_plain, content) as content_plain,
  file_url, file_name, file_size, file_type, character_count, word_count,
  status, is_submitted, submitted_at, reviewed_at, supervisor_id,
  supervisor_comments, supervisor_rating, grade, created_at, updated_at
FROM final_reports
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'iptms_v2' AND table_name = 'final_reports');

-- Rename tables to complete the migration
-- Step 1: Rename old table
RENAME TABLE final_reports TO final_reports_old;

-- Step 2: Rename new table to final_reports
RENAME TABLE final_reports_new TO final_reports;

-- Optional: Drop the old table after confirming everything works
-- DROP TABLE final_reports_old;

-- Verify the new structure
DESCRIBE final_reports;

-- Show migrated data count
SELECT COUNT(*) as migrated_records FROM final_reports;
