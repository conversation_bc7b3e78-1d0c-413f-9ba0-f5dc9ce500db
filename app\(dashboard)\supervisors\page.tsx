"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Users, 
  BookOpen, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  TrendingUp,
  Calendar
} from "lucide-react"
import { useFetchSupervisorDashboardStats } from "@/features/supervisors/api/use-fetch-supervisor-dashboard-stats"
import { useFetchAssignedStudents } from "@/features/supervisors/api/use-fetch-assigned-students"
import { useRouter } from "next/navigation"

const SupervisorDashboard = () => {
  const router = useRouter()
  const { data: stats, isLoading: isLoadingStats } = useFetchSupervisorDashboardStats()
  const { data: students = [], isLoading: isLoadingStudents } = useFetchAssignedStudents()

  // Calculate progress percentages
  const logbookProgress = stats?.total_students > 0 
    ? Math.round((stats.students_logged_this_week / stats.total_students) * 100) 
    : 0

  const reportsProgress = stats?.total_students > 0 
    ? Math.round((stats.students_with_submitted_reports / stats.total_students) * 100) 
    : 0

  const finalReportsProgress = stats?.total_students > 0 
    ? Math.round((stats.students_with_final_reports / stats.total_students) * 100) 
    : 0

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            
            {/* Header */}
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Supervisor Dashboard</h1>
              <p className="text-muted-foreground">
                Monitor and manage your assigned students' progress
              </p>
            </div>

            {/* Overview Statistics Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      stats?.total_students || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Assigned to you
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active This Week</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      stats?.students_logged_this_week || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {logbookProgress}% of students logged
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Weekly Reports</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      stats?.students_with_submitted_reports || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {reportsProgress}% submitted reports
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Final Reports</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      stats?.students_with_final_reports || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {finalReportsProgress}% completed
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Progress Overview */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Progress Overview
                  </CardTitle>
                  <CardDescription>
                    Student progress across different activities
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Logbook Activity</span>
                      <span className="text-sm text-muted-foreground">{logbookProgress}%</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${logbookProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Weekly Reports</span>
                      <span className="text-sm text-muted-foreground">{reportsProgress}%</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-orange-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${reportsProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Final Reports</span>
                      <span className="text-sm text-muted-foreground">{finalReportsProgress}%</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${finalReportsProgress}%` }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Attention Required
                  </CardTitle>
                  <CardDescription>
                    Students that need your attention
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <span className="text-sm font-medium">Pending Reports</span>
                    </div>
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                      {stats?.students_with_pending_reports || 0}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Need Feedback</span>
                    </div>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {(stats?.students_with_final_reports || 0) - (stats?.students_with_supervisor_feedback || 0)}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium">Inactive Students</span>
                    </div>
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                      {(stats?.total_students || 0) - (stats?.students_logged_this_week || 0)}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks and navigation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  <Button 
                    onClick={() => router.push('/supervisors/students')}
                    className="flex items-center gap-2"
                  >
                    <Users className="h-4 w-4" />
                    View All Students
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/supervisors/students')}
                    className="flex items-center gap-2"
                  >
                    <BookOpen className="h-4 w-4" />
                    Review Logbooks
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/supervisors/students')}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Check Reports
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Students Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Your Students</CardTitle>
                <CardDescription>
                  Quick overview of your assigned students
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingStudents ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <div className="animate-pulse bg-muted h-10 w-10 rounded-full"></div>
                        <div className="space-y-2 flex-1">
                          <div className="animate-pulse bg-muted h-4 w-32 rounded"></div>
                          <div className="animate-pulse bg-muted h-3 w-24 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : students.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No students assigned yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {students.slice(0, 5).map((student: any) => (
                      <div key={student.student_id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">
                              {student.first_name?.[0]}{student.last_name?.[0]}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{student.student_name}</p>
                            <p className="text-sm text-muted-foreground">{student.program_name}</p>
                          </div>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/supervisors/students`)}
                        >
                          View
                        </Button>
                      </div>
                    ))}
                    {students.length > 5 && (
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => router.push('/supervisors/students')}
                      >
                        View All {students.length} Students
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </div>
  )
}

export default SupervisorDashboard
