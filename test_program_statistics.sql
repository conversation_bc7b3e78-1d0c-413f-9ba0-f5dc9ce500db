-- Test script to verify program statistics are working correctly
USE iptms_v2;

-- Check current programs
SELECT 'Current Programs:' as info;
SELECT 
    id,
    name,
    description,
    created_at,
    deleted_at
FROM programs 
ORDER BY name;

-- Check users and their program assignments
SELECT 'Users by Program:' as info;
SELECT 
    p.name as program_name,
    u.role,
    COUNT(*) as user_count
FROM programs p
LEFT JOIN users u ON p.id = u.program_id AND u.deleted_at IS NULL
WHERE p.deleted_at IS NULL
GROUP BY p.id, p.name, u.role
ORDER BY p.name, u.role;

-- Test the statistics query (same as in backend)
SELECT 'Program Statistics:' as info;
SELECT 
    COUNT(DISTINCT p.id) as total_programs,
    COUNT(DISTINCT CASE WHEN u.role = 'student' AND u.deleted_at IS NULL THEN u.id END) as total_students_enrolled
FROM programs p
LEFT JOIN users u ON p.id = u.program_id
WHERE p.deleted_at IS NULL;

-- Test program breakdown with student counts
SELECT 'Program Breakdown:' as info;
SELECT 
    p.id,
    p.name,
    p.description,
    COUNT(DISTINCT CASE WHEN u.role = 'student' AND u.deleted_at IS NULL THEN u.id END) as student_count
FROM programs p
LEFT JOIN users u ON p.id = u.program_id
WHERE p.deleted_at IS NULL
GROUP BY p.id, p.name, p.description
ORDER BY student_count DESC, p.name ASC;

-- If you want to add some test data, uncomment the following:
/*
-- Add test programs if they don't exist
INSERT IGNORE INTO programs (name, description, created_at, updated_at) VALUES
('Computer Science', 'CS Program', NOW(), NOW()),
('Information Technology', 'IT Program', NOW(), NOW()),
('Software Engineering', 'SE Program', NOW(), NOW());

-- You can also add test users with program assignments:
-- INSERT INTO users (first_name, last_name, email, role, program_id, created_at, updated_at) VALUES
-- ('John', 'Doe', '<EMAIL>', 'student', 1, NOW(), NOW()),
-- ('Jane', 'Smith', '<EMAIL>', 'student', 1, NOW(), NOW()),
-- ('Bob', 'Johnson', '<EMAIL>', 'student', 2, NOW(), NOW());
*/
