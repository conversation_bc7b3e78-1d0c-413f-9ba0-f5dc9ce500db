// Test script to debug final report persistence issue
const mysql = require('mysql2/promise');

// Database configuration (adjust as needed)
const dbConfig = {
  host: 'localhost',
  user: 'root', // Change to your MySQL username
  password: '', // Change to your MySQL password
  database: 'iptms_v2'
};

async function testPersistence() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Test 1: Check current final reports
    console.log('\n📋 Current final reports in database:');
    const [currentReports] = await connection.execute(`
      SELECT id, student_id, title, status, is_submitted, 
             LENGTH(content_html) as html_length,
             LENGTH(content_plain) as plain_length,
             created_at 
      FROM final_reports 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (currentReports.length === 0) {
      console.log('   No final reports found in database');
    } else {
      currentReports.forEach(report => {
        console.log(`   ID: ${report.id}, Student: ${report.student_id}, Title: ${report.title}, Status: ${report.status}, HTML Length: ${report.html_length || 'NULL'}`);
      });
    }

    // Test 2: Insert a test record
    console.log('\n🧪 Testing insert...');
    const testData = {
      student_id: 999,
      title: 'Test Persistence Report',
      submission_type: 'write',
      content_html: '<h1>Test Content</h1><p>This is a test to check persistence.</p>',
      content_plain: 'Test Content\nThis is a test to check persistence.',
      character_count: 50,
      word_count: 10,
      status: 'submitted',
      is_submitted: 1
    };

    const [insertResult] = await connection.execute(`
      INSERT INTO final_reports (
        student_id, title, submission_type, content_html, content_plain,
        character_count, word_count, status, is_submitted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      testData.student_id,
      testData.title,
      testData.submission_type,
      testData.content_html,
      testData.content_plain,
      testData.character_count,
      testData.word_count,
      testData.status,
      testData.is_submitted
    ]);

    console.log(`✅ Insert successful, ID: ${insertResult.insertId}`);

    // Test 3: Immediately verify the insert
    const [verifyInsert] = await connection.execute(`
      SELECT id, student_id, title, status, is_submitted,
             LENGTH(content_html) as html_length,
             LENGTH(content_plain) as plain_length
      FROM final_reports 
      WHERE id = ?
    `, [insertResult.insertId]);

    if (verifyInsert.length > 0) {
      console.log('✅ Immediate verification successful:', verifyInsert[0]);
    } else {
      console.log('❌ Immediate verification failed - record not found');
    }

    // Test 4: Wait a moment and check again (simulate refresh)
    console.log('\n⏳ Waiting 2 seconds to simulate refresh...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    const [verifyAfterWait] = await connection.execute(`
      SELECT id, student_id, title, status, is_submitted,
             LENGTH(content_html) as html_length,
             LENGTH(content_plain) as plain_length
      FROM final_reports 
      WHERE id = ?
    `, [insertResult.insertId]);

    if (verifyAfterWait.length > 0) {
      console.log('✅ Post-wait verification successful:', verifyAfterWait[0]);
    } else {
      console.log('❌ Post-wait verification failed - record disappeared!');
    }

    // Test 5: Check autocommit status
    const [autocommitResult] = await connection.execute('SELECT @@autocommit as autocommit_status');
    console.log('\n🔧 Database autocommit status:', autocommitResult[0].autocommit_status === 1 ? 'ON' : 'OFF');

    // Test 6: Check transaction isolation level
    const [isolationResult] = await connection.execute('SELECT @@transaction_isolation as isolation_level');
    console.log('🔧 Transaction isolation level:', isolationResult[0].isolation_level);

    // Clean up test data
    await connection.execute('DELETE FROM final_reports WHERE id = ?', [insertResult.insertId]);
    console.log('✅ Test cleanup completed');

    console.log('\n🎉 Persistence test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Possible issues:');
    console.log('  1. Database connection problems');
    console.log('  2. Transaction not being committed');
    console.log('  3. Autocommit is disabled');
    console.log('  4. Table constraints preventing insert');
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testPersistence();
