import axios from "axios"
import { toast } from "sonner"

export interface FinalReport {
  id?: string
  student_id: string
  title: string
  submission_type: 'write' | 'upload'

  // Single content field for rich text editor
  content_html?: string
  content_plain?: string

  // File upload fields
  file_url?: string
  file_name?: string
  file_size?: number
  file_type?: string

  // Metadata
  character_count?: number
  word_count?: number

  // Status and workflow
  status: 'draft' | 'submitted' | 'reviewed' | 'approved' | 'needs_revision'
  is_submitted: boolean
  submitted_at?: string
  reviewed_at?: string

  // Supervisor feedback
  supervisor_id?: string
  supervisor_comments?: string
  supervisor_rating?: number
  grade?: string

  // Timestamps
  created_at?: string
  updated_at?: string
}

export class FinalReports {
  private static api_url = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api"

  // Get my final report
  public static async getMyFinalReport() {
    try {
            const token = localStorage.getItem('iptms_token');
      const response = await axios.get(`${this.api_url}/final-reports/my-final-report`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      if (error.response?.status !== 404) {
        toast.error(error.response?.data?.message || "Failed to fetch final report")
      }
      throw error.response?.data
    }
  }

  // Create final report
  public static async createFinalReport(finalReport: FinalReport) {
    try {
            const token = localStorage.getItem('iptms_token');
      const response = await axios.post(`${this.api_url}/final-reports/final-report`, finalReport, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Final report created successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to create final report")
      throw error.response?.data
    }
  }

  // Update final report
  public static async updateFinalReport(id: string, finalReport: Partial<FinalReport>) {
    try {
                  const token = localStorage.getItem('iptms_token');
      const response = await axios.put(`${this.api_url}/final-reports/final-report/${id}`, finalReport, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Final report updated successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update final report")
      throw error.response?.data
    }
  }

  // Delete final report
  public static async deleteFinalReport(id: string) {
    try {
                  const token = localStorage.getItem('iptms_token');
      const response = await axios.delete(`${this.api_url}/final-reports/final-report/${id}`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Final report deleted successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete final report")
      throw error.response?.data
    }
  }

  // Get all final reports (admin)
  public static async getAllFinalReports() {
    try {
                  const token = localStorage.getItem('iptms_token');
      const response = await axios.get(`${this.api_url}/final-reports/admin/final-reports`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch final reports")
      throw error.response?.data
    }
  }

  // Get final report statistics (admin)
  public static async getFinalReportStatistics() {
    try {
                  const token = localStorage.getItem('iptms_token');
      const response = await axios.get(`${this.api_url}/final-reports/admin/final-report-statistics`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch final report statistics")
      throw error.response?.data
    }
  }

  // Upload file
  public static async uploadFile(file: File) {
    try {
      const formData = new FormData();
      formData.append('file', file);

                  const token = localStorage.getItem('iptms_token');
      const response = await axios.post(`${this.api_url}/final-reports/final-report/upload`, formData, {
        headers: {
          'Authorization': token,
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        toast.success("File uploaded successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to upload file");
      throw error.response?.data;
    }
  }
}
