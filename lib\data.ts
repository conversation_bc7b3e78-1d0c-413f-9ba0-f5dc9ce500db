import { <PERSON>ge<PERSON><PERSON><PERSON>, BookCheckIcon, BookDashed, BookOpen, Bot, Frame, PieChart, Settings2, SquareTer<PERSON>l, User, User2 } from "lucide-react";


export const admin_menu_items = {
    navMain: [
        {
            title: "Dashboard",
            url: "/",
            icon: BookDashed,
            isParent: false,
        },
        {
            title: "Arrival Notes",
            url: "/admin/arrival_notes",
            icon: BadgeCheck,
            isParent: false,
        },
        {
          title: "Users",
          url: "#",
          icon: User2,
          isParent: true,
          items: [
            {
              title: "Students",
              url: "/admin/students",
            },
            {
              title: "Supervisors",
              url: "/admin/supervisors",
            }
          ],
        },
        {
          title: "Reports",
          url: "#",
          icon: BookOpen,
          isParent: true,
          items: [
            {
              title: "Student Logbooks",
              url: "/admin/reports/logbooks",
            },
            {
              title: "Final Reports",
              url: "/admin/reports/final-reports",
            },
            {
              title: "Feedback & Recommendations",
              url: "/admin/reports/feedback",
            },
            {
              title: "Reports Overview",
              url: "/admin/reports/overview",
            }
          ],
        },
        {
          title: "Management",
          url: "#",
          icon: Settings2,
          isParent: true,
          items: [
            {
              title: "Programs",
              url: "/admin/programs",
            },
            
          ],
        },
        
      ],
}

export const supervisor_menu_items = {
    navMain: [
        {
            title: "Dashboard",
            url: "/",
            icon: BookDashed,
            isParent: false,
        },
        {
            title: "Students",
            url: "/supervisors/students",
            icon: User,
            isParent: false,
        },
        
      ],
}

export const student_menu_items = {
    navMain: [
        {
            title: "Dashboard",
            url: "/",
            icon: BookDashed,
            isParent: false,
        },
        {
            title: "Arrival Note",
            url: "/students/arrival_note",
            icon: BadgeCheck,
            isParent: false,
        },
        {
          title: "Documentation",
          url: "#",
          icon: BookCheckIcon,
          isParent: true,
          items: [
            {
              title: "Logbook",
              url: "/students/logbook",
            },
            {
              title: "Final Report",
              url: "/students/final-report",
            },
            {
              title: "Feedback & Recommendations",
              url: "/students/feedback",
            }
          ],
        },
      ],
}