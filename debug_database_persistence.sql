-- Debug script to check if final reports are being saved to database
USE iptms;

-- Check if any final reports exist
SELECT COUNT(*) as total_reports FROM final_reports;

-- Show all final reports with key details
SELECT 
    id,
    student_id,
    title,
    submission_type,
    CASE 
        WHEN content_html IS NULL THEN 'NULL'
        WHEN content_html = '' THEN 'EMPTY'
        ELSE CONCAT('LENGTH: ', LENGTH(content_html))
    END as content_html_status,
    CASE 
        WHEN content_plain IS NULL THEN 'NULL'
        WHEN content_plain = '' THEN 'EMPTY'
        ELSE CONCAT('LENGTH: ', LENGTH(content_plain))
    END as content_plain_status,
    character_count,
    word_count,
    status,
    is_submitted,
    created_at,
    updated_at
FROM final_reports 
ORDER BY created_at DESC 
LIMIT 10;

-- Check for any recent inserts (last 1 hour)
SELECT 
    id,
    student_id,
    title,
    status,
    is_submitted,
    created_at
FROM final_reports 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY created_at DESC;

-- Check if there are any auto-commit issues
SHOW VARIABLES LIKE 'autocommit';
