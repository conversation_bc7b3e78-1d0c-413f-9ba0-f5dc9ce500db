// Test script to verify final reports functionality
const mysql = require('mysql2/promise');

// Database configuration (adjust as needed)
const dbConfig = {
  host: 'localhost',
  user: 'root', // Change to your MySQL username
  password: 'password', // Change to your MySQL password
  database: 'iptms'
};

async function testFinalReports() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check if final_reports table exists
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.tables 
      WHERE table_schema = 'iptms_v2' AND table_name = 'final_reports'
    `);

    if (tables.length === 0) {
      console.log('❌ final_reports table does not exist');
      console.log('Please run the improved_final_reports_table.sql script first');
      return;
    }

    console.log('✅ final_reports table exists');

    // Check table structure
    const [columns] = await connection.execute('DESCRIBE final_reports');
    console.log('\n📋 Table structure:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });

    // Check if we have the new simplified columns
    const hasContentHtml = columns.some(col => col.Field === 'content_html');
    const hasContentPlain = columns.some(col => col.Field === 'content_plain');
    const hasOldColumns = columns.some(col => col.Field === 'executive_summary');

    if (hasContentHtml && hasContentPlain && !hasOldColumns) {
      console.log('✅ Table has the new simplified structure');
    } else if (hasOldColumns) {
      console.log('⚠️  Table still has old structure - migration needed');
      console.log('Please run: mysql -u root -p iptms_v2 < update_final_reports_table.sql');
    } else {
      console.log('❓ Table structure is unclear');
    }

    // Test basic operations
    console.log('\n🧪 Testing basic operations...');

    // Test insert
    const testData = {
      student_id: 999, // Use a test student ID
      title: 'Test Final Report',
      submission_type: 'write',
      content_html: '<h1>Test Report</h1><p>This is a test report content.</p>',
      content_plain: 'Test Report\nThis is a test report content.',
      character_count: 45,
      word_count: 8,
      status: 'draft',
      is_submitted: 0
    };

    try {
      const [insertResult] = await connection.execute(`
        INSERT INTO final_reports (
          student_id, title, submission_type, content_html, content_plain,
          character_count, word_count, status, is_submitted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testData.student_id,
        testData.title,
        testData.submission_type,
        testData.content_html,
        testData.content_plain,
        testData.character_count,
        testData.word_count,
        testData.status,
        testData.is_submitted
      ]);

      console.log('✅ Insert test successful, ID:', insertResult.insertId);

      // Test select
      const [selectResult] = await connection.execute(
        'SELECT * FROM final_reports WHERE id = ?',
        [insertResult.insertId]
      );

      if (selectResult.length > 0) {
        console.log('✅ Select test successful');
        console.log('📄 Retrieved data:', {
          id: selectResult[0].id,
          title: selectResult[0].title,
          content_length: selectResult[0].content_html?.length || 0,
          status: selectResult[0].status
        });
      }

      // Test update
      const [updateResult] = await connection.execute(`
        UPDATE final_reports SET 
          status = 'submitted', 
          is_submitted = 1,
          submitted_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [insertResult.insertId]);

      if (updateResult.affectedRows > 0) {
        console.log('✅ Update test successful');
      }

      // Clean up test data
      await connection.execute('DELETE FROM final_reports WHERE id = ?', [insertResult.insertId]);
      console.log('✅ Test cleanup completed');

    } catch (insertError) {
      console.log('❌ Database operation failed:', insertError.message);
      
      if (insertError.code === 'ER_NO_SUCH_TABLE') {
        console.log('💡 Please create the final_reports table first');
      } else if (insertError.code === 'ER_BAD_FIELD_ERROR') {
        console.log('💡 Table structure mismatch - migration needed');
      }
    }

    console.log('\n🎉 Final reports database test completed!');

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n💡 Make sure:');
    console.log('  1. MySQL server is running');
    console.log('  2. Database credentials are correct');
    console.log('  3. iptms_v2 database exists');
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testFinalReports();
