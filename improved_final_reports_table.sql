-- Improved Final Reports Table Structure
-- This replaces the overly complex structure with a simpler, more practical design
-- that matches the actual UI implementation with a single rich text editor

USE iptms_v2;

-- Drop the existing table if it exists (be careful in production!)
-- DROP TABLE IF EXISTS final_reports;

-- Create the improved final_reports table
CREATE TABLE IF NOT EXISTS `final_reports` (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL,
  `title` varchar(255) NOT NULL DEFAULT 'Final Internship Report',
  `submission_type` enum('write','upload') NOT NULL DEFAULT 'write',
  
  -- Single content field for rich text editor (main approach)
  `content_html` longtext COMMENT 'Rich text HTML content from editor',
  `content_plain` longtext COMMENT 'Plain text version for search/analysis',
  
  -- File upload fields (alternative approach)
  `file_url` varchar(500) DEFAULT NULL COMMENT 'URL/path to uploaded file',
  `file_name` varchar(255) DEFAULT NULL COMMENT 'Original filename',
  `file_size` bigint DEFAULT NULL COMMENT 'File size in bytes',
  `file_type` varchar(100) DEFAULT NULL COMMENT 'MIME type of uploaded file',
  
  -- Metadata
  `character_count` int DEFAULT NULL COMMENT 'Character count of content',
  `word_count` int DEFAULT NULL COMMENT 'Word count of content',
  
  -- Status and workflow
  `status` enum('draft','submitted','reviewed','approved','needs_revision') NOT NULL DEFAULT 'draft',
  `is_submitted` tinyint(1) DEFAULT '0',
  `submitted_at` timestamp NULL DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  
  -- Supervisor feedback
  `supervisor_id` int DEFAULT NULL,
  `supervisor_comments` text COMMENT 'Supervisor feedback on the report',
  `supervisor_rating` int DEFAULT NULL COMMENT 'Rating from 1-10',
  `grade` varchar(10) DEFAULT NULL COMMENT 'Final grade (A, B, C, etc.)',
  
  -- Timestamps
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_final_report` (`student_id`),
  KEY `idx_final_reports_student_id` (`student_id`),
  KEY `idx_final_reports_status` (`status`),
  KEY `idx_final_reports_submitted` (`is_submitted`),
  KEY `idx_final_reports_supervisor_id` (`supervisor_id`),
  
  -- Foreign key constraints
  CONSTRAINT `fk_final_reports_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_final_reports_supervisor` FOREIGN KEY (`supervisor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  
  -- Check constraints
  CONSTRAINT `chk_final_reports_rating` CHECK (`supervisor_rating` >= 1 AND `supervisor_rating` <= 10),
  CONSTRAINT `chk_final_reports_content` CHECK (
    (`submission_type` = 'write' AND `content_html` IS NOT NULL) OR 
    (`submission_type` = 'upload' AND `file_url` IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- If you need to migrate existing data, uncomment and modify this:
/*
-- Migrate existing data if the old table exists
INSERT INTO final_reports (
  student_id, title, submission_type, content_html, content_plain,
  file_url, file_name, file_size, file_type, character_count, word_count,
  status, is_submitted, submitted_at, reviewed_at, supervisor_id,
  supervisor_comments, supervisor_rating, grade, created_at, updated_at
)
SELECT 
  student_id, title, submission_type,
  -- Combine all the separate fields into single HTML content
  CONCAT_WS('<br><br>',
    CASE WHEN executive_summary IS NOT NULL THEN CONCAT('<h3>Executive Summary</h3>', executive_summary_html) END,
    CASE WHEN objectives_achieved IS NOT NULL THEN CONCAT('<h3>Objectives Achieved</h3>', objectives_achieved_html) END,
    CASE WHEN challenges_faced IS NOT NULL THEN CONCAT('<h3>Challenges Faced</h3>', challenges_faced_html) END,
    CASE WHEN skills_acquired IS NOT NULL THEN CONCAT('<h3>Skills Acquired</h3>', skills_acquired_html) END,
    CASE WHEN recommendations IS NOT NULL THEN CONCAT('<h3>Recommendations</h3>', recommendations_html) END,
    CASE WHEN conclusion IS NOT NULL THEN CONCAT('<h3>Conclusion</h3>', conclusion_html) END
  ) as content_html,
  content_plain,
  file_url, file_name, file_size, file_type, character_count, word_count,
  status, is_submitted, submitted_at, reviewed_at, supervisor_id,
  supervisor_comments, supervisor_rating, grade, created_at, updated_at
FROM old_final_reports_table;
*/
