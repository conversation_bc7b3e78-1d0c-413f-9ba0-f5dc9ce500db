import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useAddLogbookComment = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation<any, Error, { logbook_id: string; comment: string }>({ 
        mutationFn: async ({ logbook_id, comment }: { logbook_id: string; comment: string }) => {
            const res = await Supervisor.addLogbookComment(logbook_id, comment);
            return res;
        },
        onSuccess: (data, variables) => {
            // Invalidate related queries
            queryClient.invalidateQueries({ queryKey: ["supervisor-student-details"] });
            queryClient.invalidateQueries({ queryKey: ["supervisor-assigned-students"] });
        },
        onError: (error) => {
            console.error('Add logbook comment error:', error);
        },
    });

    return mutation;
};
