import axios from "axios";
import { toast } from "sonner";

export interface ProgramData {
  id?: string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export class Program {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;

  // Get all programs
  public static async index() {
    try {
      const response = await axios.get(`${this.api_url}/other/programs`);

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch programs");
      throw error.response?.data;
    }
  }

  // Keep the old method for backward compatibility
  public static async getPrograms() {
    try {
      const response = await axios.get(`${this.api_url}/other/getPrograms`);

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch programs");
      throw error.response?.data;
    }
  }

  // Get single program by ID
  public static async show(id: string) {
    try {
      const token = localStorage.getItem('iptms_token');
      const response = await axios.get(`${this.api_url}/other/program/${id}`, {
        headers: {
          'Authorization': token
        }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch program");
      throw error.response?.data;
    }
  }

  // Create new program (admin only)
  public static async create(data: ProgramData) {
    try {
      const token = localStorage.getItem('iptms_token');
      const response = await axios.post(`${this.api_url}/other/admin/programs`, data, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 201) {
        toast.success("Program created successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to create program");
      throw error.response?.data;
    }
  }

  // Update existing program (admin only)
  public static async update(id: string, data: ProgramData) {
    try {
      const token = localStorage.getItem('iptms_token');
      const response = await axios.put(`${this.api_url}/other/admin/programs/${id}`, data, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        toast.success("Program updated successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update program");
      throw error.response?.data;
    }
  }

  // Delete program (admin only)
  public static async destroy(id: string) {
    try {
      const token = localStorage.getItem('iptms_token');
      const response = await axios.delete(`${this.api_url}/other/admin/programs/${id}`, {
        headers: {
          'Authorization': token
        }
      });

      if (response.status === 200) {
        toast.success("Program deleted successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete program");
      throw error.response?.data;
    }
  }

  // Get program statistics (admin only)
  public static async getStatistics() {
    try {
      const token = localStorage.getItem('iptms_token');
      const response = await axios.get(`${this.api_url}/other/admin/program-statistics`, {
        headers: {
          'Authorization': token
        }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch program statistics");
      throw error.response?.data;
    }
  }
}
